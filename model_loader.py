#!/usr/bin/env python3
"""
Model loader for gpt-oss-20b with fallback options
"""

from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import os

class ModelLoader:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = None
        self.tokenizer = None
        self.use_mock = False
        
    def load_model(self, model_path="openai/gpt-oss-20b"):
        """Load the model with proper error handling"""
        try:
            print(f"Loading model from {model_path}...")
            print(f"Device: {self.device}")
            
            # Load tokenizer first
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            print("✓ Tokenizer loaded")
            
            # Load model with appropriate settings
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True  # May be needed for some models
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
                
            self.model.eval()
            print(f"✓ Model loaded on {self.device}")
            return True
            
        except Exception as e:
            print(f"Failed to load model: {e}")
            print("Falling back to mock mode...")
            self.use_mock = True
            return False
    
    def generate_response(self, prompt, max_length=512, temperature=1.0, do_sample=True):
        """Generate response with the loaded model"""
        if self.use_mock:
            return f"[MOCK RESPONSE] Input was: {prompt[:50]}..."
        
        try:
            inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=max_length,
                    temperature=temperature,
                    do_sample=do_sample,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            # Remove the original prompt from response
            response = response[len(prompt):].strip()
            return response
            
        except Exception as e:
            print(f"Generation error: {e}")
            return f"[ERROR] {str(e)}"

def test_model_loading():
    """Test the model loading"""
    loader = ModelLoader()
    
    # Try loading the model
    success = loader.load_model()
    
    if success:
        print("\n=== Testing model generation ===")
        test_prompt = "Hello, I am"
        response = loader.generate_response(test_prompt, max_length=50)
        print(f"Prompt: {test_prompt}")
        print(f"Response: {response}")
    else:
        print("Model loading failed - check your setup")
    
    return loader

if __name__ == "__main__":
    test_model_loading()