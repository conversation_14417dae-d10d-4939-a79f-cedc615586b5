{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://example.org/schemas/redteam-issue.schema.json", "title": "Red Teaming Challenge Issue (Single Finding)", "type": "object", "additionalProperties": false, "properties": {"schema_version": {"type": "string", "description": "Semantic version of this schema. Example: 1.0.0"}, "issue_title": {"type": "string", "description": "Title of the issue found"}, "model": {"type": "object", "additionalProperties": false, "required": ["name"], "properties": {"name": {"type": "string", "description": "e.g., gpt-oss-20b"}, "version": {"type": "string", "description": "Model release tag or checkpoint hash"}, "provider": {"type": "string", "description": "Org or repo"}, "source_url": {"type": "string", "format": "uri"}, "parameters": {"type": "object", "description": "Inference parameters used when reproducing", "additionalProperties": true, "properties": {"temperature": {"type": "number"}, "reasoning_level": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "max_output_tokens": {"type": "integer"}, "seed": {"type": "integer", "description": "For deterministic runs if supported"}}}}}, "environment": {"type": "object", "additionalProperties": false, "properties": {"endpoint": {"type": "string", "description": "If you used a hosted endpoint or API, please say which one."}, "hardware": {"type": "string", "description": "GPU/CPU type and RAM"}, "libraries": {"type": "array", "items": {"type": "string"}, "description": "Key libraries used (transformers, vLLM, etc.)"}}}, "issue_summary": {"type": "object", "additionalProperties": false, "required": ["title", "overview", "desired_behavior", "observed_behavior"], "properties": {"title": {"type": "string"}, "topic_area": {"type": "string", "description": "Which of the topics of interest does this most closely align to?"}, "overview": {"type": "string", "description": "High-level narrative of the issue; 1000 characters max", "maxLength": 1000}, "desired_behavior": {"type": "string", "description": "What would the desirable behavior of the system be in this scenario?  1000 characters max.", "maxLength": 1000}, "observed_behavior": {"type": "string", "description": "What was the observed / problematic behavior displayed?  1000 characters max.", "maxLength": 1000}, "self_assessed_severity": {"type": "string", "description": "Refer to the grading rubric and give your self assessment for the severity score"}, "self_assessed_severity_justification": {"type": "string", "description": "Why do you feel that severity is the correct score?  500 characters max.", "maxLength": 500}, "self_assessed_breadth": {"type": "string", "description": "Refer to the grading rubric and give your self assessment for the severity score"}, "self_assessed_breadth_justification": {"type": "string", "description": "Why do you feel that breadth is the correct score?  500 characters max.", "maxLength": 500}}}, "harmony_response_walkthroughs": {"type": "array", "description": "One or more canonical walkthroughs in harmony response format that reproduce the issue.", "minItems": 1, "maxItems": 10, "items": {"type": "string", "maxLength": 20000, "description": "A full conversation as a string in harmony response format. Be sure to escape properly for JSON compatibility."}}, "steps_to_reproduce": {"type": "array", "description": "Optionally give any additional step-by-step instructions for reproducing this issue.", "minItems": 0, "maxItems": 10, "items": {"type": "string", "maxLength": 1000, "description": "Instructions for one step."}}, "notes": {"type": "string", "description": "Anything else reviewers should know", "maxLength": 2000}}}